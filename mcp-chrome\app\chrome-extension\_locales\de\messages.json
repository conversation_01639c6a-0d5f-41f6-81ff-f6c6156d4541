{"extensionName": {"message": "chrome-mcp-server", "description": "Erweiterungsname"}, "extensionDescription": {"message": "Stellt Browser-Funktionen mit Ihrem eigenen Chrome zur Verfügung", "description": "Erweiterungsbeschreibung"}, "nativeServerConfigLabel": {"message": "Native Server-Konfiguration", "description": "Hauptabschnittstitel für Native Server-Einstellungen"}, "semanticEngineLabel": {"message": "Semantische Engine", "description": "Hauptabschnittstitel für semantische Engine"}, "embeddingModelLabel": {"message": "Embedding-Modell", "description": "Hauptabschnittstitel für Modellauswahl"}, "indexDataManagementLabel": {"message": "Index-Datenverwaltung", "description": "Hauptabschnittstitel für Datenverwaltung"}, "modelCacheManagementLabel": {"message": "<PERSON>l<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Hauptabschnittstitel für Cache-Verwaltung"}, "statusLabel": {"message": "Status", "description": "Allgemeines Statuslabel"}, "runningStatusLabel": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Server-Betriebsstatuslabel"}, "connectionStatusLabel": {"message": "Verbindungss<PERSON>us", "description": "Verbindungsstatuslabel"}, "lastUpdatedLabel": {"message": "Zuletzt aktualisiert:", "description": "Zeitstempel der letzten Aktualisierung"}, "connectButton": {"message": "Verbinden", "description": "Verbinden-Schaltflächentext"}, "disconnectButton": {"message": "<PERSON><PERSON><PERSON>", "description": "Trennen-Schaltflächentext"}, "connectingStatus": {"message": "Verbindung wird hergestellt...", "description": "Verbindungsstatusmeldung"}, "connectedStatus": {"message": "Verbunden", "description": "Verbunden-Statusmeldung"}, "disconnectedStatus": {"message": "Getrennt", "description": "Getrennt-Statusmeldung"}, "detectingStatus": {"message": "Erkennung läuft...", "description": "Erkennungsstatusmeldung"}, "serviceRunningStatus": {"message": "Service läuft (Port: $PORT$)", "description": "Service läuft mit Portnummer", "placeholders": {"port": {"content": "$1", "example": "12306"}}}, "serviceNotConnectedStatus": {"message": "Service nicht verbunden", "description": "Service nicht verbunden Status"}, "connectedServiceNotStartedStatus": {"message": "Verbunden, Service nicht gestartet", "description": "Verbunden aber Service nicht gestartet Status"}, "mcpServerConfigLabel": {"message": "MCP Server-Konfiguration", "description": "MCP Server-Konfigurationsabschnittslabel"}, "connectionPortLabel": {"message": "Verbindungsport", "description": "Verbindungsport-Eingabelabel"}, "refreshStatusButton": {"message": "Status aktualisieren", "description": "Status aktualisieren Schaltflächen-Tooltip"}, "copyConfigButton": {"message": "Konfiguration kopieren", "description": "Konfiguration kopieren Schaltflächentext"}, "retryButton": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Wiederholen-Schaltflächentext"}, "cancelButton": {"message": "Abbrechen", "description": "Abbrechen-Schaltflächentext"}, "confirmButton": {"message": "Bestätigen", "description": "Bestätigen-Schaltflächentext"}, "saveButton": {"message": "Speichern", "description": "Speichern-Schaltflächentext"}, "closeButton": {"message": "Schließen", "description": "Schließen-Schaltflächentext"}, "resetButton": {"message": "Z<PERSON>ücksetzen", "description": "Zurücksetzen-Schaltflächentext"}, "initializingStatus": {"message": "Initialisierung...", "description": "Initialisierung-Fortschrittsmeldung"}, "processingStatus": {"message": "Verarbeitung...", "description": "Verarbeitung-Fortschrittsmeldung"}, "loadingStatus": {"message": "Wird geladen...", "description": "Ladefortschrittsmeldung"}, "clearingStatus": {"message": "Wird geleert...", "description": "Leerungsfortschrittsmeldung"}, "cleaningStatus": {"message": "Wird bereinigt...", "description": "Bereinigungsfortschrittsmeldung"}, "downloadingStatus": {"message": "Wird herunt<PERSON><PERSON><PERSON><PERSON>...", "description": "Download-Fortschrittsmeldung"}, "semanticEngineReadyStatus": {"message": "Semantische Engine bereit", "description": "Semantische Engine bereit Status"}, "semanticEngineInitializingStatus": {"message": "Semantische Engine wird initialisiert...", "description": "Semantische Engine Initialisierungsstatus"}, "semanticEngineInitFailedStatus": {"message": "Initialisierung der semantischen Engine fehlgeschlagen", "description": "Semantische Engine Initialisierung fehlgeschlagen Status"}, "semanticEngineNotInitStatus": {"message": "Semantische Engine nicht initialisiert", "description": "Semantische Engine nicht initialisiert Status"}, "initSemanticEngineButton": {"message": "Semantische Engine initialisieren", "description": "Semantische Engine initialisieren Schaltflächentext"}, "reinitializeButton": {"message": "Neu initialisieren", "description": "Neu initialisieren Schaltflächentext"}, "downloadingModelStatus": {"message": "Modell wird heruntergeladen... $PROGRESS$%", "description": "Modell-Download-Fortschritt mit Prozentsatz", "placeholders": {"progress": {"content": "$1", "example": "50"}}}, "switchingModelStatus": {"message": "Modell wird gewechselt...", "description": "Modellwechsel-Fortschrittsmeldung"}, "modelLoadedStatus": {"message": "<PERSON>l geladen", "description": "Modell erfolgreich geladen Status"}, "modelFailedStatus": {"message": "<PERSON><PERSON> konnte nicht geladen werden", "description": "Modell-Ladefehler Status"}, "lightweightModelDescription": {"message": "Leichtgewichtiges mehrsprachiges Modell", "description": "Beschreibung für leichtgewichtige Modelloption"}, "betterThanSmallDescription": {"message": "Etwas größer als e5-small, aber bessere Leistung", "description": "Beschreibung für mittlere Modelloption"}, "multilingualModelDescription": {"message": "Mehrsprachiges semantisches Modell", "description": "Beschreibung für mehrsprachige Modelloption"}, "fastPerformance": {"message": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>sanzeige"}, "balancedPerformance": {"message": "Ausgewogen", "description": "Ausgewogene Leistungsanzeige"}, "accuratePerformance": {"message": "<PERSON><PERSON>", "description": "Genaue Leistungsanzeige"}, "networkErrorMessage": {"message": "Netzwerkverbindungsfehler, bitte Netzwerk prüfen und erneut versuchen", "description": "Netzwerkverbindungsfehlermeldung"}, "modelCorruptedErrorMessage": {"message": "Modelldatei beschädigt oder unvollständig, bitte Download wiederholen", "description": "Modell-Beschädigungsfehlermeldung"}, "unknownErrorMessage": {"message": "<PERSON><PERSON><PERSON><PERSON>, bit<PERSON> p<PERSON><PERSON><PERSON>, ob Ihr Netzwerk auf HuggingFace zugreifen kann", "description": "Unbekannte Fehler-Rückfallmeldung"}, "permissionDeniedErrorMessage": {"message": "<PERSON><PERSON><PERSON> verweigert", "description": "Zugriff verweigert Fehlermeldung"}, "timeoutErrorMessage": {"message": "Zeitüberschreitung", "description": "Zeitüberschreitungsfehlermeldung"}, "indexedPagesLabel": {"message": "Indizierte Seiten", "description": "Anzahl indizierter Seiten Label"}, "indexSizeLabel": {"message": "Indexgröße", "description": "Indexgröße Label"}, "activeTabsLabel": {"message": "Aktive Tabs", "description": "Anzahl aktiver Tabs Label"}, "vectorDocumentsLabel": {"message": "Vektordokumente", "description": "Anzahl Vektordokumente Label"}, "cacheSizeLabel": {"message": "Cache-Größe", "description": "Cache-Größe Label"}, "cacheEntriesLabel": {"message": "Cache-Einträge", "description": "<PERSON><PERSON>hl <PERSON>ache-Einträge Label"}, "clearAllDataButton": {"message": "Alle Daten löschen", "description": "Alle Daten löschen Schaltflächentext"}, "clearAllCacheButton": {"message": "Gesamten Cache löschen", "description": "Gesamten Cache löschen Schaltflächentext"}, "cleanExpiredCacheButton": {"message": "Abgelaufenen Cache bereinigen", "description": "Abgelaufenen Cache bereinigen Schaltflächentext"}, "exportDataButton": {"message": "Daten exportieren", "description": "Daten exportieren Schaltflächentext"}, "importDataButton": {"message": "Daten importieren", "description": "Daten importieren Schaltflächentext"}, "confirmClearDataTitle": {"message": "Datenlöschung bestätigen", "description": "Datenlöschung bestätigen Dialogtitel"}, "settingsTitle": {"message": "Einstellungen", "description": "Einstellungen Dialogtitel"}, "aboutTitle": {"message": "<PERSON><PERSON>", "description": "Über Dialogtitel"}, "helpTitle": {"message": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>"}, "clearDataWarningMessage": {"message": "Diese Aktion löscht alle indizierten Webseiteninhalte und Vektordaten, e<PERSON><PERSON><PERSON><PERSON>lich:", "description": "Datenlöschung Warnmeldung"}, "clearDataList1": {"message": "Alle Webseitentextinhaltsindizes", "description": "Erster Punkt in Datenlöschungsliste"}, "clearDataList2": {"message": "Vektor-Embedding-Daten", "description": "Zweiter Punkt in Datenlöschungsliste"}, "clearDataList3": {"message": "<PERSON><PERSON><PERSON><PERSON> und <PERSON>", "description": "Dritter <PERSON> in Datenlöschungsliste"}, "clearDataIrreversibleWarning": {"message": "Diese Aktion ist unwiderruflich! Nach dem Löschen müssen Sie Webseiten erneut durchsuchen, um den Index neu aufzubauen.", "description": "Unwiderrufliche Aktion Warnung"}, "confirmClearButton": {"message": "Löschung bestätigen", "description": "Löschung bestätigen Aktionsschaltfläche"}, "cacheDetailsLabel": {"message": "<PERSON><PERSON>-Details", "description": "Cache-Details Abschnittslabel"}, "noCacheDataMessage": {"message": "<PERSON><PERSON>-<PERSON><PERSON> v<PERSON>", "description": "<PERSON><PERSON>-<PERSON><PERSON>"}, "loadingCacheInfoStatus": {"message": "Cache-Informationen werden geladen...", "description": "Cache-<PERSON><PERSON> laden Status"}, "processingCacheStatus": {"message": "<PERSON>ache wird verarbeitet...", "description": "<PERSON><PERSON> verar<PERSON>"}, "expiredLabel": {"message": "Abgelaufen", "description": "Abgelaufenes Element Label"}, "bookmarksBarLabel": {"message": "Lesezeichenleiste", "description": "Lesezeichenleiste Ordnername"}, "newTabLabel": {"message": "<PERSON><PERSON><PERSON>", "description": "Neuer Tab Label"}, "currentPageLabel": {"message": "Aktuelle Seite", "description": "Aktuelle Seite Label"}, "menuLabel": {"message": "<PERSON><PERSON>", "description": "Menü Barrierefreiheitslabel"}, "navigationLabel": {"message": "Navigation", "description": "Navigation Barrierefreiheitslabel"}, "mainContentLabel": {"message": "<PERSON><PERSON><PERSON><PERSON>", "description": "Hauptinhalt Barrierefreiheitslabel"}, "languageSelectorLabel": {"message": "<PERSON><PERSON><PERSON>", "description": "Sprachauswahl Label"}, "themeLabel": {"message": "Design", "description": "Design-Auswahl Label"}, "lightTheme": {"message": "Hell", "description": "Helles Design Option"}, "darkTheme": {"message": "<PERSON><PERSON><PERSON>", "description": "Dunkles Design Option"}, "autoTheme": {"message": "Automatisch", "description": "Automatisches Design Option"}, "advancedSettingsLabel": {"message": "Erweiterte Einstellungen", "description": "Erweiterte Einstellungen Abschnittslabel"}, "debugModeLabel": {"message": "Debug-Modus", "description": "Debug-Modus Umschalter Label"}, "verboseLoggingLabel": {"message": "Ausführliche Protokollierung", "description": "Ausführliche Protokollierung Umschalter Label"}, "successNotification": {"message": "Vorgang erfolgreich abgeschlossen", "description": "Allgemeine Erfolgsmeldung"}, "warningNotification": {"message": "Warnung: Bitte prüfen Sie vor dem Fortfahren", "description": "Allgemeine Warnmeldung"}, "infoNotification": {"message": "Information", "description": "Allgemeine Informationsmeldung"}, "configCopiedNotification": {"message": "Konfiguration in Zwischenablage kopiert", "description": "Konfiguration kopiert Erfolgsmeldung"}, "dataClearedNotification": {"message": "Daten erfolg<PERSON><PERSON>", "description": "Daten gelöscht Erfolgsmeldung"}, "bytesUnit": {"message": "Bytes", "description": "Bytes Einheit"}, "kilobytesUnit": {"message": "KB", "description": "Kilobytes Einheit"}, "megabytesUnit": {"message": "MB", "description": "Megabytes Einheit"}, "gigabytesUnit": {"message": "GB", "description": "Gigabytes Einheit"}, "itemsUnit": {"message": "Elemente", "description": "Elemente Zähleinheit"}, "pagesUnit": {"message": "Seiten", "description": "Seiten Zähleinheit"}}