# Chrome MCP Server 安装指南

## 🎉 安装完成状态

✅ **mcp-chrome-bridge** 已成功安装并注册  
✅ **Chrome扩展文件** 已下载并解压  
✅ **MCP服务器** 正在端口12306上运行  

## 📋 下一步：在Chrome中安装扩展

### 1. 打开Chrome扩展管理页面
1. 打开Chrome浏览器
2. 在地址栏输入：`chrome://extensions/`
3. 按回车键

### 2. 启用开发者模式
1. 在扩展页面右上角找到"开发者模式"开关
2. 点击开关启用开发者模式

### 3. 加载扩展
1. 点击"加载已解压的扩展程序"按钮
2. 浏览到以下文件夹：
   ```
   C:\Cursor\test\chrome-mcp-extension
   ```
3. 选择该文件夹并点击"选择文件夹"

### 4. 验证安装
1. 扩展安装后，您应该在Chrome工具栏看到MCP Chrome图标
2. 点击扩展图标打开插件面板
3. 点击"Connect"按钮连接到MCP服务器
4. 如果连接成功，您会看到MCP配置信息

## 🔧 配置MCP客户端

### 使用Streamable HTTP连接（推荐）
将以下配置添加到您的MCP客户端配置中：

```json
{
  "mcpServers": {
    "chrome-mcp-server": {
      "type": "streamableHttp",
      "url": "http://127.0.0.1:12306/mcp"
    }
  }
}
```

### 在Augment Code中配置
1. 打开Augment Code设置
2. 找到MCP服务器配置部分
3. 添加新的MCP服务器：
   - **名称**: `chrome-mcp-server`
   - **类型**: `streamableHttp`
   - **URL**: `http://127.0.0.1:12306/mcp`

## 🛠️ 可用工具

Chrome MCP Server提供20+种工具，包括：

### 📊 浏览器管理 (6个工具)
- `get_windows_and_tabs` - 列出所有浏览器窗口和标签页
- `chrome_navigate` - 导航到URL并控制视口
- `chrome_close_tabs` - 关闭特定标签页或窗口
- `chrome_go_back_or_forward` - 浏览器导航控制
- `chrome_inject_script` - 向网页注入内容脚本
- `chrome_send_command_to_inject_script` - 向注入的内容脚本发送命令

### 📸 截图和视觉 (1个工具)
- `chrome_screenshot` - 高级截图捕获，支持元素定位、全页面和自定义尺寸

### 🌐 网络监控 (4个工具)
- `chrome_network_capture_start/stop` - webRequest API网络捕获
- `chrome_network_debugger_start/stop` - 带响应体的调试器API
- `chrome_network_request` - 发送自定义HTTP请求

### 🔍 内容分析 (4个工具)
- `search_tabs_content` - AI驱动的浏览器标签页语义搜索
- `chrome_get_web_content` - 从页面提取HTML/文本内容
- `chrome_get_interactive_elements` - 查找可点击元素
- `chrome_console` - 从浏览器标签页捕获和检索控制台输出

### 🎯 交互 (3个工具)
- `chrome_click_element` - 使用CSS选择器点击元素
- `chrome_fill_or_select` - 填写表单和选择选项
- `chrome_keyboard` - 模拟键盘输入和快捷键

### 📚 数据管理 (5个工具)
- `chrome_history` - 使用时间过滤器搜索浏览器历史
- `chrome_bookmark_search` - 按关键词查找书签
- `chrome_bookmark_add` - 添加支持文件夹的新书签
- `chrome_bookmark_delete` - 删除书签

## 🚀 使用示例

### 基本用法
安装完成后，您可以通过AI助手使用以下命令：

1. **截图**: "请帮我截取当前页面的截图"
2. **内容分析**: "总结当前网页的内容"
3. **自动化操作**: "帮我填写这个表单"
4. **网络监控**: "监控这个页面的网络请求"
5. **书签管理**: "将当前页面添加到书签"

## 🔧 故障排除

### 如果扩展无法连接到服务器：
1. 确认MCP服务器正在运行：
   ```powershell
   netstat -an | findstr 12306
   ```
2. 重新注册Native Messaging host：
   ```powershell
   mcp-chrome-bridge register
   ```

### 如果扩展无法加载：
1. 检查Chrome扩展页面是否有错误信息
2. 确认开发者模式已启用
3. 尝试重新加载扩展

## 📞 获取帮助

如果遇到问题，可以：
1. 查看项目文档：https://github.com/hangwin/mcp-chrome
2. 检查GitHub Issues：https://github.com/hangwin/mcp-chrome/issues
3. 查看故障排除指南：https://github.com/hangwin/mcp-chrome/blob/master/docs/TROUBLESHOOTING.md

## 🎯 下一步

安装完成后，您就可以：
1. 让AI助手控制您的Chrome浏览器
2. 自动化网页操作和数据提取
3. 进行智能内容分析和搜索
4. 监控网络请求和性能

享受AI驱动的浏览器自动化体验！🚀
