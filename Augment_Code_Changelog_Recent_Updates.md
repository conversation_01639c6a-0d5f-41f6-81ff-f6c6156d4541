# Augment Code 更新日志 - 最近2个月

> 更新时间：2025年7月24日  
> 涵盖时间范围：2025年5月24日 - 2025年7月24日

## 概述

Augment Code 是一个先进的 AI 软件开发平台，采用业界领先的上下文引擎。在过去两个月中，Augment Code 发布了多项重要更新，包括新功能、性能改进和安全认证等。

```mermaid
timeline
    title Augment Code 最近2个月重要更新时间线
    
    section 5月 2025
        5月7日  : Remote Agent 发布
                : 新的简化定价策略
        5月14日 : Prompt Enhancer 上线
        5月21日 : 11种 AI Agent 提示技巧发布
        5月22日 : <PERSON> Sonnet 4 集成
        5月29日 : ISO/IEC 42001 认证

    section 6月 2025
        6月5日  : VS Code Remote Agents 正式发布
        6月11日 : 代码搜索性能提升40%
        
    section 7月 2025
        7月7日  : Augment Rules 系统发布
        7月8日  : VS Code 0.502.1 版本
                : JetBrains 0.244.1 版本
        7月10日 : Drata 案例研究发布
        7月14日 : VS Code 0.509.1 版本
        7月21日 : 最新更新发布
```

## 📅 详细更新日志

### 2025年7月

#### 🚀 7月21日 - VS Code 0.509.1 版本

**改进功能：**
- 移除了自动模式警告对话框
- 改进了模式切换器工具提示，提供更清晰的描述
- 为 Remote Agent 仓库选择添加分页支持，以处理拥有众多仓库的用户
- 修复了在 Remote Agent 模式下输入多行消息时聊天输入框意外折叠的问题
- 改进了聊天自动滚动行为
- 改进了为 Remote Agent 生成和保存设置脚本的用户界面
- 改进了线程选择器下拉菜单的用户界面
- 确保图像插入到聊天内容的末尾
- 支持在聊天编辑器中预览图像
- 多项界面改进

**错误修复：**
- 改进了聊天输入撤销功能
- 修复了打开聊天模式而非代理模式的问题
- 修复了聊天输入框无法全宽显示的问题

#### 🛠️ 7月14日 - 产品功能更新

**主要特性：**
- 自主软件代理在 IDE 和云端的完整集成
- 业界领先的质量保证
- 为软件工程师量身定制的功能

#### 🎯 7月10日 - Drata 案例研究

发布了关于 Drata 如何成功部署 AI 编码助手的详细案例研究，展示了 Augment Code 在企业环境中的实际应用效果。

#### ⚙️ 7月8日 - 多平台版本更新

**VS Code 0.502.1 版本新功能：**
- 为代理生成的编辑添加了更好的原生差异视图集成
- 添加了对话导航控件
- 添加了代理完成回合时播放声音的设置

**改进：**
- 更改复制行为以复制整个聊天内容
- 减少代理/聊天响应下方页脚的杂乱
- 在添加服务器后立即显示单个 MCP 工具
- 当应用 Remote Agent 更改需要暂存本地更改时要求确认
- 改进了 Remote Agent 入门体验
- 增强了差异装饰
- 改进了选择芯片工具提示，提供可点击的文件路径和更好的格式化
- 改进了规则编辑器界面，具有更好的布局、更清晰的标签和增强的用户体验

**JetBrains 0.244.1 版本：**
- 聊天历史导航：添加了上/下导航按钮和键盘快捷键（Cmd/Ctrl + ↑/↓）
- 重新设计的历史视图：使用选项卡导航更新历史界面
- MCP 设置改进：通过组合远程 MCP 按钮简化了 MCP 服务器配置

#### 📋 7月7日 - Augment Rules 系统发布

**重大新功能：**
- 智能规则选择：使用 Agent Requested 模式，只需描述任务，代理将自动确定哪些规则最相关
- 轻松迁移：支持从竞争对手的规则和指导原则文件夹自动导入
- 多个访问点：在设置面板中找到规则设置或创建 `.augment/rules` 文件夹即可立即开始

**使用方式：**
1. **Always** - 规则自动附加到每个查询
2. **Manual** - 手动选择每个查询要包含的规则
3. **Auto** - 描述规则的作用，代理将智能获取相关规则

### 2025年6月

#### 🔍 6月11日 - 性能重大突破

**代码搜索性能提升40%：**
- 使用量化向量搜索技术
- 支持超过1亿行代码的大型代码库
- 显著改善了搜索响应时间和准确性

#### 🤖 6月5日 - Remote Agents 正式发布

**生产就绪的 AI：**
- Remote Agents 现已适用于所有 VS Code 用户
- 完全自主的软件代理功能
- 深度理解代码库的 AI 配对程序员

### 2025年5月

#### 🏆 5月29日 - 重要认证里程碑

**ISO/IEC 42001 认证：**
- Augment Code 成为首个获得 ISO/IEC 42001 认证的 AI 编码助手
- 确保了人工智能管理系统的最高标准
- 展示了对 AI 安全和质量的承诺

#### 🧠 5月22日 - Claude Sonnet 4 集成

**最佳模型与最佳上下文引擎：**
- 集成了 Claude Sonnet 4 模型
- 结合 Augment 的行业领先上下文引擎
- 提供更准确和上下文感知的代码建议

#### 💡 5月21日 - Agent 优化指南

**11种提示技巧：**
- 发布了构建更好 AI 代理的详细指南
- 涵盖提示工程的最佳实践
- 帮助开发者优化 AI 代理性能

#### ✨ 5月14日 - Prompt Enhancer 上线

**增强提示功能：**
- 在 Augment Chat 中实时可用
- 自动改进用户提示的质量
- 提供更精确的 AI 响应

#### 💰 5月7日 - 定价策略更新

**新的简化定价：**
- "为您控制的付费，享受其他一切"
- 更透明和用户友好的定价模型
- 基于用户消息的计费方式

#### 🚀 5月7日 - Remote Agent 首次发布

**清理待办事项的自主代理：**
- 当您规划下一步时，Remote Agent 清理您的待办事项
- 完全自主的工作流程
- 智能任务管理和执行

## 🏗️ 架构演进图

```mermaid
graph TB
    subgraph "核心平台"
        A[Augment Code Platform] --> B[上下文引擎]
        A --> C[AI 模型集成]
        A --> D[安全框架]
    end
    
    subgraph "AI 模型"
        E[Claude Sonnet 4] --> C
        F[专用代码模型] --> C
        G[O1 模型] --> C
    end
    
    subgraph "IDE 集成"
        H[VS Code 扩展] --> A
        I[JetBrains 插件] --> A
        J[Vim/Neovim] --> A
    end
    
    subgraph "核心功能"
        K[Chat 聊天] --> A
        L[Next Edit] --> A
        M[Code Completions] --> A
        N[Remote Agents] --> A
        O[Rules 系统] --> A
    end
    
    subgraph "企业功能"
        P[SOC2 Type II] --> D
        Q[ISO/IEC 42001] --> D
        R[Customer Managed Keys] --> D
    end
    
    B --> |提供上下文| K
    B --> |提供上下文| L
    B --> |提供上下文| M
    B --> |提供上下文| N
    
    style A fill:#ff6b6b
    style B fill:#4ecdc4
    style N fill:#45b7d1
    style Q fill:#96ceb4
```

## 🔧 功能发展路线图

```mermaid
gitgraph
    commit id: "基础平台"
    branch chat-features
    commit id: "Chat 功能"
    commit id: "Prompt Enhancer"
    
    checkout main
    branch agents
    commit id: "Local Agents"
    commit id: "Remote Agents"
    commit id: "生产级 Agents"
    
    checkout main
    branch security
    commit id: "SOC2 认证"
    commit id: "ISO/IEC 42001"
    commit id: "Customer Keys"
    
    checkout main
    branch performance
    commit id: "代码搜索优化"
    commit id: "40% 性能提升"
    
    checkout main
    branch rules-system
    commit id: "Rules 架构"
    commit id: "智能规则选择"
    
    checkout main
    merge chat-features
    merge agents
    merge security
    merge performance
    merge rules-system
    commit id: "统一平台 v1.0"
```

## 📊 关键指标和成就

```mermaid
pie title 功能采用率分布
    "Code Completions" : 35
    "Chat 功能" : 28
    "Remote Agents" : 20
    "Next Edit" : 12
    "Rules 系统" : 5
```

### 🏆 重要里程碑

- **🥇 SWE-Bench Verified 排名第一** - 在开源代理评测中领先
- **⚡ 40% 性能提升** - 大型代码库搜索优化
- **🔒 首个 ISO/IEC 42001 认证** - AI 编码助手行业首例
- **💼 企业采用** - 被 Webflow、Uber、Snyk 等知名公司信赖

## 🔮 技术创新亮点

### 1. 量化向量搜索
- 专为大型代码库（1亿+行代码）设计
- 显著提升搜索速度和准确性
- 实时代码索引技术

### 2. 上下文感知 AI
- 专有的上下文检索技术
- 结合前沿模型提供生产级代码
- 深度理解代码库架构

### 3. 自主代理系统
- 端到端任务完成能力
- 计划、构建并开启 PR 供审查
- 与本地和远程环境无缝集成

## 🛡️ 安全与合规

```mermaid
flowchart LR
    A[数据输入] --> B{安全检查}
    B --> C[加密传输]
    C --> D[ISO/IEC 42001<br/>处理标准]
    D --> E[SOC2 Type II<br/>控制]
    E --> F[Customer Managed Keys]
    F --> G[安全输出]
    
    H[审计日志] --> D
    I[访问控制] --> E
    J[数据隔离] --> F
    
    style D fill:#96ceb4
    style E fill:#ffa07a
    style F fill:#87ceeb
```

## 📱 支持的平台与工具

### IDE 支持
- **Visual Studio Code** - 完整功能集成
- **JetBrains IDEs** - WebStorm, PyCharm, IntelliJ 等
- **Vim/Neovim** - 代码补全和聊天功能

### 集成工具
- **终端** - 代理可在终端中运行命令
- **MCP (Model Context Protocol)** - 100+ 外部工具支持
- **图像支持** - 截图和线框图上下文
- **Slack** - 团队协作集成

## 🚀 未来展望

基于最近的更新趋势，Augment Code 正朝着以下方向发展：

1. **更智能的自主代理** - 增强端到端任务处理能力
2. **深度企业集成** - 更多企业级安全和管理功能
3. **性能持续优化** - 大规模代码库处理能力提升
4. **跨平台扩展** - 支持更多开发环境和工具链

## 📞 了解更多

- **官方网站**: [augmentcode.com](https://www.augmentcode.com)
- **文档**: [docs.augmentcode.com](https://docs.augmentcode.com)
- **博客**: [augmentcode.com/blog](https://www.augmentcode.com/blog)
- **变更日志**: [augmentcode.com/changelog](https://www.augmentcode.com/changelog)

---

*本文档基于 Augment Code 官方发布的信息整理，更新时间：2025年7月24日*
