var ortWasmThreaded = (() => {
  var _scriptName = import.meta.url;
  
  return (
async function(moduleArg = {}) {
  var moduleRtn;

var e=moduleArg,aa,ca,da=new Promise((a,b)=>{aa=a;ca=b}),ea="object"==typeof window,k="undefined"!=typeof WorkerGlobalScope,n="object"==typeof process&&"object"==typeof process.versions&&"string"==typeof process.versions.node&&"renderer"!=process.type,q=k&&self.name?.startsWith("em-pthread");if(n){const {createRequire:a}=await import("module");var require=a(import.meta.url),fa=require("worker_threads");global.Worker=fa.Worker;q=(k=!fa.pc)&&"em-pthread"==fa.workerData}
e.mountExternalData=(a,b)=>{a.startsWith("./")&&(a=a.substring(2));(e.Fb||(e.Fb=new Map)).set(a,b)};e.unmountExternalData=()=>{delete e.Fb};var SharedArrayBuffer=globalThis.SharedArrayBuffer??(new WebAssembly.Memory({initial:0,maximum:0,qc:!0})).buffer.constructor;
const ha=a=>async(...b)=>{try{if(e.Gb)throw Error("Session already started");const c=e.Gb={ec:b[0],errors:[]},d=await a(...b);if(e.Gb!==c)throw Error("Session mismatch");e.Kb?.flush();const f=c.errors;if(0<f.length){let g=await Promise.all(f);g=g.filter(h=>h);if(0<g.length)throw Error(g.join("\n"));}return d}finally{e.Gb=null}};
e.jsepInit=(a,b)=>{if("webgpu"===a){[e.Kb,e.Vb,e.Zb,e.Lb,e.Yb,e.kb,e.$b,e.bc,e.Wb,e.Xb,e.ac]=b;const c=e.Kb;e.jsepRegisterBuffer=(d,f,g,h)=>c.registerBuffer(d,f,g,h);e.jsepGetBuffer=d=>c.getBuffer(d);e.jsepCreateDownloader=(d,f,g)=>c.createDownloader(d,f,g);e.jsepOnCreateSession=d=>{c.onCreateSession(d)};e.jsepOnReleaseSession=d=>{c.onReleaseSession(d)};e.jsepOnRunStart=d=>c.onRunStart(d);e.cc=(d,f)=>{c.upload(d,f)}}else if("webnn"===a){const c=b[0];[e.oc,e.Ob,e.webnnEnsureTensor,e.Pb,e.webnnDownloadTensor]=
b.slice(1);e.webnnReleaseTensorId=e.Ob;e.webnnUploadTensor=e.Pb;e.webnnOnRunStart=d=>c.onRunStart(d);e.webnnOnRunEnd=c.onRunEnd.bind(c);e.webnnRegisterMLContext=(d,f)=>{c.registerMLContext(d,f)};e.webnnOnReleaseSession=d=>{c.onReleaseSession(d)};e.webnnCreateMLTensorDownloader=(d,f)=>c.createMLTensorDownloader(d,f);e.webnnRegisterMLTensor=(d,f,g,h)=>c.registerMLTensor(d,f,g,h);e.webnnCreateMLContext=d=>c.createMLContext(d);e.webnnRegisterMLConstant=(d,f,g,h,l,m)=>c.registerMLConstant(d,f,g,h,l,e.Fb,
m);e.webnnRegisterGraphInput=c.registerGraphInput.bind(c);e.webnnIsGraphInput=c.isGraphInput.bind(c);e.webnnRegisterGraphOutput=c.registerGraphOutput.bind(c);e.webnnIsGraphOutput=c.isGraphOutput.bind(c);e.webnnCreateTemporaryTensor=c.createTemporaryTensor.bind(c);e.webnnIsGraphInputOutputTypeSupported=c.isGraphInputOutputTypeSupported.bind(c)}};
let ja=()=>{const a=(b,c,d)=>(...f)=>{const g=t,h=c?.();f=b(...f);const l=c?.();h!==l&&(b=l,d(h),c=d=null);return t!=g?ia():f};(b=>{for(const c of b)e[c]=a(e[c],()=>e[c],d=>e[c]=d)})(["_OrtAppendExecutionProvider","_OrtCreateSession","_OrtRun","_OrtRunWithBinding","_OrtBindInput"]);"undefined"!==typeof ha&&(e._OrtRun=ha(e._OrtRun),e._OrtRunWithBinding=ha(e._OrtRunWithBinding));ja=void 0};e.asyncInit=()=>{ja?.()};var ka=Object.assign({},e),la="./this.program",ma=(a,b)=>{throw b;},v="",na,oa;
if(n){var fs=require("fs"),pa=require("path");import.meta.url.startsWith("data:")||(v=pa.dirname(require("url").fileURLToPath(import.meta.url))+"/");oa=a=>{a=qa(a)?new URL(a):a;return fs.readFileSync(a)};na=async a=>{a=qa(a)?new URL(a):a;return fs.readFileSync(a,void 0)};!e.thisProgram&&1<process.argv.length&&(la=process.argv[1].replace(/\\/g,"/"));process.argv.slice(2);ma=(a,b)=>{process.exitCode=a;throw b;}}else if(ea||k)k?v=self.location.href:"undefined"!=typeof document&&
document.currentScript&&(v=document.currentScript.src),_scriptName&&(v=_scriptName),v.startsWith("blob:")?v="":v=v.slice(0,v.replace(/[?#].*/,"").lastIndexOf("/")+1),n||(k&&(oa=a=>{var b=new XMLHttpRequest;b.open("GET",a,!1);b.responseType="arraybuffer";b.send(null);return new Uint8Array(b.response)}),na=async a=>{if(qa(a))return new Promise((c,d)=>{var f=new XMLHttpRequest;f.open("GET",a,!0);f.responseType="arraybuffer";f.onload=()=>{200==f.status||0==f.status&&f.response?c(f.response):d(f.status)};
f.onerror=d;f.send(null)});var b=await fetch(a,{credentials:"same-origin"});if(b.ok)return b.arrayBuffer();throw Error(b.status+" : "+b.url);});var ra=console.log.bind(console),sa=console.error.bind(console);n&&(ra=(...a)=>fs.writeSync(1,a.join(" ")+"\n"),sa=(...a)=>fs.writeSync(2,a.join(" ")+"\n"));var ta=ra,x=sa;Object.assign(e,ka);ka=null;var ua=e.wasmBinary,z,va,A=!1,wa,B,xa,ya,za,Aa,Ba,Ca,C,Da,Ea,qa=a=>a.startsWith("file://");function D(){z.buffer!=B.buffer&&E();return B}
function F(){z.buffer!=B.buffer&&E();return xa}function G(){z.buffer!=B.buffer&&E();return ya}function Fa(){z.buffer!=B.buffer&&E();return za}function H(){z.buffer!=B.buffer&&E();return Aa}function I(){z.buffer!=B.buffer&&E();return Ba}function Ga(){z.buffer!=B.buffer&&E();return Ca}function J(){z.buffer!=B.buffer&&E();return Ea}
if(q){var Ha;if(n){var Ia=fa.parentPort;Ia.on("message",b=>onmessage({data:b}));Object.assign(globalThis,{self:global,postMessage:b=>Ia.postMessage(b)})}var Ja=!1;x=function(...b){b=b.join(" ");n?fs.writeSync(2,b+"\n"):console.error(b)};self.alert=function(...b){postMessage({Cb:"alert",text:b.join(" "),jc:Ka()})};self.onunhandledrejection=b=>{throw b.reason||b;};function a(b){try{var c=b.data,d=c.Cb;if("load"===d){let f=[];self.onmessage=g=>f.push(g);self.startWorker=()=>{postMessage({Cb:"loaded"});
for(let g of f)a(g);self.onmessage=a};for(const g of c.Sb)if(!e[g]||e[g].proxy)e[g]=(...h)=>{postMessage({Cb:"callHandler",Rb:g,args:h})},"print"==g&&(ta=e[g]),"printErr"==g&&(x=e[g]);z=c.lc;E();Ha(c.mc)}else if("run"===d){La(c.Bb);Ma(c.Bb,0,0,1,0,0);Na();Oa(c.Bb);Ja||(Pa(),Ja=!0);try{Qa(c.hc,c.Ib)}catch(f){if("unwind"!=f)throw f;}}else"setimmediate"!==c.target&&("checkMailbox"===d?Ja&&Ra():d&&(x(`worker: received unknown command ${d}`),x(c)))}catch(f){throw Sa(),f;}}self.onmessage=a}
function E(){var a=z.buffer;e.HEAP8=B=new Int8Array(a);e.HEAP16=ya=new Int16Array(a);e.HEAPU8=xa=new Uint8Array(a);e.HEAPU16=za=new Uint16Array(a);e.HEAP32=Aa=new Int32Array(a);e.HEAPU32=Ba=new Uint32Array(a);e.HEAPF32=Ca=new Float32Array(a);e.HEAPF64=Ea=new Float64Array(a);e.HEAP64=C=new BigInt64Array(a);e.HEAPU64=Da=new BigUint64Array(a)}q||(z=new WebAssembly.Memory({initial:256,maximum:65536,shared:!0}),E());function Ta(){q?startWorker(e):K.Da()}var Ua=0,Va=null;
function Wa(){Ua--;if(0==Ua&&Va){var a=Va;Va=null;a()}}function L(a){a="Aborted("+a+")";x(a);A=!0;a=new WebAssembly.RuntimeError(a+". Build with -sASSERTIONS for more info.");ca(a);throw a;}var Xa;async function Ya(a){if(!ua)try{var b=await na(a);return new Uint8Array(b)}catch{}if(a==Xa&&ua)a=new Uint8Array(ua);else if(oa)a=oa(a);else throw"both async and sync fetching of the wasm failed";return a}
async function Za(a,b){try{var c=await Ya(a);return await WebAssembly.instantiate(c,b)}catch(d){x(`failed to asynchronously prepare wasm: ${d}`),L(d)}}async function $a(a){var b=Xa;if(!ua&&"function"==typeof WebAssembly.instantiateStreaming&&!qa(b)&&!n)try{var c=fetch(b,{credentials:"same-origin"});return await WebAssembly.instantiateStreaming(c,a)}catch(d){x(`wasm streaming compile failed: ${d}`),x("falling back to ArrayBuffer instantiation")}return Za(b,a)}
function ab(){bb={L:cb,Aa:db,b:eb,$:fb,A:gb,pa:hb,X:ib,Z:jb,qa:kb,na:lb,ga:mb,ma:nb,J:ob,Y:pb,V:qb,oa:rb,W:sb,va:tb,E:ub,Q:vb,O:wb,D:xb,v:yb,r:zb,P:Ab,z:Bb,R:Cb,ja:Db,T:Eb,aa:Fb,M:Gb,F:Hb,ia:Oa,sa:Ib,t:Jb,Ca:Kb,w:Lb,o:Mb,m:Nb,c:Ob,Ba:Pb,n:Qb,j:Rb,u:Sb,p:Tb,f:Ub,s:Vb,l:Wb,e:Xb,k:Yb,h:Zb,g:$b,d:ac,da:bc,ea:cc,fa:dc,ba:ec,ca:fc,N:gc,xa:hc,ua:ic,i:jc,C:kc,G:lc,ta:mc,x:nc,ra:oc,U:pc,q:qc,y:rc,K:sc,S:tc,za:uc,ya:vc,ka:wc,la:xc,_:yc,B:zc,I:Ac,ha:Bc,H:Cc,a:z,wa:Dc};return{a:bb}}
var Ec={840156:(a,b,c,d,f)=>{if("undefined"==typeof e||!e.Fb)return 1;a=M(Number(a>>>0));a.startsWith("./")&&(a=a.substring(2));a=e.Fb.get(a);if(!a)return 2;b=Number(b>>>0);c=Number(c>>>0);d=Number(d>>>0);if(b+c>a.byteLength)return 3;try{const g=a.subarray(b,b+c);switch(f){case 0:F().set(g,d>>>0);break;case 1:e.nc?e.nc(d,g):e.cc(d,g);break;default:return 4}return 0}catch{return 4}},840980:(a,b,c)=>{e.Pb(a,F().subarray(b>>>0,b+c>>>0))},841044:()=>e.oc(),841086:a=>{e.Ob(a)},841123:()=>{e.Wb()},841154:()=>
{e.Xb()},841183:()=>{e.ac()},841208:a=>e.Vb(a),841241:a=>e.Zb(a),841273:(a,b,c)=>{e.Lb(Number(a),Number(b),Number(c),!0)},841336:(a,b,c)=>{e.Lb(Number(a),Number(b),Number(c))},841393:()=>"undefined"!==typeof wasmOffsetConverter,841450:a=>{e.kb("Abs",a,void 0)},841501:a=>{e.kb("Neg",a,void 0)},841552:a=>{e.kb("Floor",a,void 0)},841605:a=>{e.kb("Ceil",a,void 0)},841657:a=>{e.kb("Reciprocal",a,void 0)},841715:a=>{e.kb("Sqrt",a,void 0)},841767:a=>{e.kb("Exp",a,void 0)},841818:a=>{e.kb("Erf",a,void 0)},
841869:a=>{e.kb("Sigmoid",a,void 0)},841924:(a,b,c)=>{e.kb("HardSigmoid",a,{alpha:b,beta:c})},842003:a=>{e.kb("Log",a,void 0)},842054:a=>{e.kb("Sin",a,void 0)},842105:a=>{e.kb("Cos",a,void 0)},842156:a=>{e.kb("Tan",a,void 0)},842207:a=>{e.kb("Asin",a,void 0)},842259:a=>{e.kb("Acos",a,void 0)},842311:a=>{e.kb("Atan",a,void 0)},842363:a=>{e.kb("Sinh",a,void 0)},842415:a=>{e.kb("Cosh",a,void 0)},842467:a=>{e.kb("Asinh",a,void 0)},842520:a=>{e.kb("Acosh",a,void 0)},842573:a=>{e.kb("Atanh",a,void 0)},
842626:a=>{e.kb("Tanh",a,void 0)},842678:a=>{e.kb("Not",a,void 0)},842729:(a,b,c)=>{e.kb("Clip",a,{min:b,max:c})},842798:a=>{e.kb("Clip",a,void 0)},842850:(a,b)=>{e.kb("Elu",a,{alpha:b})},842908:a=>{e.kb("Gelu",a,void 0)},842960:a=>{e.kb("Relu",a,void 0)},843012:(a,b)=>{e.kb("LeakyRelu",a,{alpha:b})},843076:(a,b)=>{e.kb("ThresholdedRelu",a,{alpha:b})},843146:(a,b)=>{e.kb("Cast",a,{to:b})},843204:a=>{e.kb("Add",a,void 0)},843255:a=>{e.kb("Sub",a,void 0)},843306:a=>{e.kb("Mul",a,void 0)},843357:a=>
{e.kb("Div",a,void 0)},843408:a=>{e.kb("Pow",a,void 0)},843459:a=>{e.kb("Equal",a,void 0)},843512:a=>{e.kb("Greater",a,void 0)},843567:a=>{e.kb("GreaterOrEqual",a,void 0)},843629:a=>{e.kb("Less",a,void 0)},843681:a=>{e.kb("LessOrEqual",a,void 0)},843740:(a,b,c,d,f)=>{e.kb("ReduceMean",a,{keepDims:!!b,noopWithEmptyAxes:!!c,axes:d?Array.from(H().subarray(Number(d)>>>0,Number(f)>>>0)):[]})},843915:(a,b,c,d,f)=>{e.kb("ReduceMax",a,{keepDims:!!b,noopWithEmptyAxes:!!c,axes:d?Array.from(H().subarray(Number(d)>>>
0,Number(f)>>>0)):[]})},844089:(a,b,c,d,f)=>{e.kb("ReduceMin",a,{keepDims:!!b,noopWithEmptyAxes:!!c,axes:d?Array.from(H().subarray(Number(d)>>>0,Number(f)>>>0)):[]})},844263:(a,b,c,d,f)=>{e.kb("ReduceProd",a,{keepDims:!!b,noopWithEmptyAxes:!!c,axes:d?Array.from(H().subarray(Number(d)>>>0,Number(f)>>>0)):[]})},844438:(a,b,c,d,f)=>{e.kb("ReduceSum",a,{keepDims:!!b,noopWithEmptyAxes:!!c,axes:d?Array.from(H().subarray(Number(d)>>>0,Number(f)>>>0)):[]})},844612:(a,b,c,d,f)=>{e.kb("ReduceL1",a,{keepDims:!!b,
noopWithEmptyAxes:!!c,axes:d?Array.from(H().subarray(Number(d)>>>0,Number(f)>>>0)):[]})},844785:(a,b,c,d,f)=>{e.kb("ReduceL2",a,{keepDims:!!b,noopWithEmptyAxes:!!c,axes:d?Array.from(H().subarray(Number(d)>>>0,Number(f)>>>0)):[]})},844958:(a,b,c,d,f)=>{e.kb("ReduceLogSum",a,{keepDims:!!b,noopWithEmptyAxes:!!c,axes:d?Array.from(H().subarray(Number(d)>>>0,Number(f)>>>0)):[]})},845135:(a,b,c,d,f)=>{e.kb("ReduceSumSquare",a,{keepDims:!!b,noopWithEmptyAxes:!!c,axes:d?Array.from(H().subarray(Number(d)>>>
0,Number(f)>>>0)):[]})},845315:(a,b,c,d,f)=>{e.kb("ReduceLogSumExp",a,{keepDims:!!b,noopWithEmptyAxes:!!c,axes:d?Array.from(H().subarray(Number(d)>>>0,Number(f)>>>0)):[]})},845495:a=>{e.kb("Where",a,void 0)},845548:(a,b,c)=>{e.kb("Transpose",a,{perm:b?Array.from(H().subarray(Number(b)>>>0,Number(c)>>>0)):[]})},845672:(a,b,c,d)=>{e.kb("DepthToSpace",a,{blocksize:b,mode:M(c),format:d?"NHWC":"NCHW"})},845805:(a,b,c,d)=>{e.kb("DepthToSpace",a,{blocksize:b,mode:M(c),format:d?"NHWC":"NCHW"})},845938:(a,
b,c,d,f,g,h,l,m,p,r,u,w,y,ba)=>{e.kb("ConvTranspose",a,{format:m?"NHWC":"NCHW",autoPad:b,dilations:[c],group:d,kernelShape:[f],pads:[g,h],strides:[l],wIsConst:()=>!!D()[p>>>0],outputPadding:r?Array.from(H().subarray(Number(r)>>>0,Number(u)>>>0)):[],outputShape:w?Array.from(H().subarray(Number(w)>>>0,Number(y)>>>0)):[],activation:M(ba)})},846371:(a,b,c,d,f,g,h,l,m,p,r,u,w,y)=>{e.kb("ConvTranspose",a,{format:l?"NHWC":"NCHW",autoPad:b,dilations:Array.from(H().subarray(Number(c)>>>0,(Number(c)>>>0)+2>>>
0)),group:d,kernelShape:Array.from(H().subarray(Number(f)>>>0,(Number(f)>>>0)+2>>>0)),pads:Array.from(H().subarray(Number(g)>>>0,(Number(g)>>>0)+4>>>0)),strides:Array.from(H().subarray(Number(h)>>>0,(Number(h)>>>0)+2>>>0)),wIsConst:()=>!!D()[m>>>0],outputPadding:p?Array.from(H().subarray(Number(p)>>>0,Number(r)>>>0)):[],outputShape:u?Array.from(H().subarray(Number(u)>>>0,Number(w)>>>0)):[],activation:M(y)})},847032:(a,b,c,d,f,g,h,l,m,p,r,u,w,y,ba)=>{e.kb("ConvTranspose",a,{format:m?"NHWC":"NCHW",
autoPad:b,dilations:[c],group:d,kernelShape:[f],pads:[g,h],strides:[l],wIsConst:()=>!!D()[p>>>0],outputPadding:r?Array.from(H().subarray(Number(r)>>>0,Number(u)>>>0)):[],outputShape:w?Array.from(H().subarray(Number(w)>>>0,Number(y)>>>0)):[],activation:M(ba)})},847465:(a,b,c,d,f,g,h,l,m,p,r,u,w,y)=>{e.kb("ConvTranspose",a,{format:l?"NHWC":"NCHW",autoPad:b,dilations:Array.from(H().subarray(Number(c)>>>0,(Number(c)>>>0)+2>>>0)),group:d,kernelShape:Array.from(H().subarray(Number(f)>>>0,(Number(f)>>>0)+
2>>>0)),pads:Array.from(H().subarray(Number(g)>>>0,(Number(g)>>>0)+4>>>0)),strides:Array.from(H().subarray(Number(h)>>>0,(Number(h)>>>0)+2>>>0)),wIsConst:()=>!!D()[m>>>0],outputPadding:p?Array.from(H().subarray(Number(p)>>>0,Number(r)>>>0)):[],outputShape:u?Array.from(H().subarray(Number(u)>>>0,Number(w)>>>0)):[],activation:M(y)})},848126:(a,b)=>{e.kb("GlobalAveragePool",a,{format:b?"NHWC":"NCHW"})},848217:(a,b,c,d,f,g,h,l,m,p,r,u,w,y)=>{e.kb("AveragePool",a,{format:y?"NHWC":"NCHW",auto_pad:b,ceil_mode:c,
count_include_pad:d,storage_order:f,dilations:g?Array.from(H().subarray(Number(g)>>>0,Number(h)>>>0)):[],kernel_shape:l?Array.from(H().subarray(Number(l)>>>0,Number(m)>>>0)):[],pads:p?Array.from(H().subarray(Number(p)>>>0,Number(r)>>>0)):[],strides:u?Array.from(H().subarray(Number(u)>>>0,Number(w)>>>0)):[]})},848696:(a,b)=>{e.kb("GlobalAveragePool",a,{format:b?"NHWC":"NCHW"})},848787:(a,b,c,d,f,g,h,l,m,p,r,u,w,y)=>{e.kb("AveragePool",a,{format:y?"NHWC":"NCHW",auto_pad:b,ceil_mode:c,count_include_pad:d,
storage_order:f,dilations:g?Array.from(H().subarray(Number(g)>>>0,Number(h)>>>0)):[],kernel_shape:l?Array.from(H().subarray(Number(l)>>>0,Number(m)>>>0)):[],pads:p?Array.from(H().subarray(Number(p)>>>0,Number(r)>>>0)):[],strides:u?Array.from(H().subarray(Number(u)>>>0,Number(w)>>>0)):[]})},849266:(a,b)=>{e.kb("GlobalMaxPool",a,{format:b?"NHWC":"NCHW"})},849353:(a,b,c,d,f,g,h,l,m,p,r,u,w,y)=>{e.kb("MaxPool",a,{format:y?"NHWC":"NCHW",auto_pad:b,ceil_mode:c,count_include_pad:d,storage_order:f,dilations:g?
Array.from(H().subarray(Number(g)>>>0,Number(h)>>>0)):[],kernel_shape:l?Array.from(H().subarray(Number(l)>>>0,Number(m)>>>0)):[],pads:p?Array.from(H().subarray(Number(p)>>>0,Number(r)>>>0)):[],strides:u?Array.from(H().subarray(Number(u)>>>0,Number(w)>>>0)):[]})},849828:(a,b)=>{e.kb("GlobalMaxPool",a,{format:b?"NHWC":"NCHW"})},849915:(a,b,c,d,f,g,h,l,m,p,r,u,w,y)=>{e.kb("MaxPool",a,{format:y?"NHWC":"NCHW",auto_pad:b,ceil_mode:c,count_include_pad:d,storage_order:f,dilations:g?Array.from(H().subarray(Number(g)>>>
0,Number(h)>>>0)):[],kernel_shape:l?Array.from(H().subarray(Number(l)>>>0,Number(m)>>>0)):[],pads:p?Array.from(H().subarray(Number(p)>>>0,Number(r)>>>0)):[],strides:u?Array.from(H().subarray(Number(u)>>>0,Number(w)>>>0)):[]})},850390:(a,b,c,d,f)=>{e.kb("Gemm",a,{alpha:b,beta:c,transA:d,transB:f})},850494:a=>{e.kb("MatMul",a,void 0)},850548:(a,b,c,d)=>{e.kb("ArgMax",a,{keepDims:!!b,selectLastIndex:!!c,axis:d})},850656:(a,b,c,d)=>{e.kb("ArgMin",a,{keepDims:!!b,selectLastIndex:!!c,axis:d})},850764:(a,
b)=>{e.kb("Softmax",a,{axis:b})},850827:(a,b)=>{e.kb("Concat",a,{axis:b})},850887:(a,b,c,d,f)=>{e.kb("Split",a,{axis:b,numOutputs:c,splitSizes:d?Array.from(H().subarray(Number(d)>>>0,Number(f)>>>0)):[]})},851043:a=>{e.kb("Expand",a,void 0)},851097:(a,b)=>{e.kb("Gather",a,{axis:Number(b)})},851168:(a,b)=>{e.kb("GatherElements",a,{axis:Number(b)})},851247:(a,b)=>{e.kb("GatherND",a,{batch_dims:Number(b)})},851326:(a,b,c,d,f,g,h,l,m,p,r)=>{e.kb("Resize",a,{antialias:b,axes:c?Array.from(H().subarray(Number(c)>>>
0,Number(d)>>>0)):[],coordinateTransformMode:M(f),cubicCoeffA:g,excludeOutside:h,extrapolationValue:l,keepAspectRatioPolicy:M(m),mode:M(p),nearestMode:M(r)})},851688:(a,b,c,d,f,g,h)=>{e.kb("Slice",a,{starts:b?Array.from(H().subarray(Number(b)>>>0,Number(c)>>>0)):[],ends:d?Array.from(H().subarray(Number(d)>>>0,Number(f)>>>0)):[],axes:g?Array.from(H().subarray(Number(g)>>>0,Number(h)>>>0)):[]})},851952:a=>{e.kb("Tile",a,void 0)},852004:(a,b,c)=>{e.kb("InstanceNormalization",a,{epsilon:b,format:c?"NHWC":
"NCHW"})},852118:(a,b,c)=>{e.kb("InstanceNormalization",a,{epsilon:b,format:c?"NHWC":"NCHW"})},852232:a=>{e.kb("Range",a,void 0)},852285:(a,b)=>{e.kb("Einsum",a,{equation:M(b)})},852366:(a,b,c,d,f)=>{e.kb("Pad",a,{mode:b,value:c,pads:d?Array.from(H().subarray(Number(d)>>>0,Number(f)>>>0)):[]})},852509:(a,b,c,d,f,g)=>{e.kb("BatchNormalization",a,{epsilon:b,momentum:c,spatial:!!f,trainingMode:!!d,format:g?"NHWC":"NCHW"})},852678:(a,b,c,d,f,g)=>{e.kb("BatchNormalization",a,{epsilon:b,momentum:c,spatial:!!f,
trainingMode:!!d,format:g?"NHWC":"NCHW"})},852847:(a,b,c)=>{e.kb("CumSum",a,{exclusive:Number(b),reverse:Number(c)})},852944:(a,b,c)=>{e.kb("DequantizeLinear",a,{axis:b,blockSize:c})},853034:(a,b,c,d,f)=>{e.kb("GridSample",a,{align_corners:b,mode:M(c),padding_mode:M(d),format:f?"NHWC":"NCHW"})},853204:(a,b,c,d,f)=>{e.kb("GridSample",a,{align_corners:b,mode:M(c),padding_mode:M(d),format:f?"NHWC":"NCHW"})},853374:(a,b)=>{e.kb("ScatterND",a,{reduction:M(b)})},853459:(a,b,c,d,f,g,h,l,m)=>{e.kb("Attention",
a,{numHeads:b,isUnidirectional:c,maskFilterValue:d,scale:f,doRotary:g,qkvHiddenSizes:h?Array.from(H().subarray(Number(l)>>>0,Number(l)+h>>>0)):[],pastPresentShareBuffer:!!m})},853731:a=>{e.kb("BiasAdd",a,void 0)},853786:a=>{e.kb("BiasSplitGelu",a,void 0)},853847:a=>{e.kb("FastGelu",a,void 0)},853903:(a,b,c,d,f,g,h,l,m,p,r,u,w,y,ba,Wd)=>{e.kb("Conv",a,{format:u?"NHWC":"NCHW",auto_pad:b,dilations:c?Array.from(H().subarray(Number(c)>>>0,Number(d)>>>0)):[],group:f,kernel_shape:g?Array.from(H().subarray(Number(g)>>>
0,Number(h)>>>0)):[],pads:l?Array.from(H().subarray(Number(l)>>>0,Number(m)>>>0)):[],strides:p?Array.from(H().subarray(Number(p)>>>0,Number(r)>>>0)):[],w_is_const:()=>!!D()[Number(w)>>>0],activation:M(y),activation_params:ba?Array.from(Ga().subarray(Number(ba)>>>0,Number(Wd)>>>0)):[]})},854487:a=>{e.kb("Gelu",a,void 0)},854539:(a,b,c,d,f,g,h,l,m)=>{e.kb("GroupQueryAttention",a,{numHeads:b,kvNumHeads:c,scale:d,softcap:f,doRotary:g,rotaryInterleaved:h,smoothSoftmax:l,localWindowSize:m})},854756:(a,
b,c,d)=>{e.kb("LayerNormalization",a,{axis:b,epsilon:c,simplified:!!d})},854867:(a,b,c,d)=>{e.kb("LayerNormalization",a,{axis:b,epsilon:c,simplified:!!d})},854978:(a,b,c,d,f,g)=>{e.kb("MatMulNBits",a,{k:b,n:c,accuracyLevel:d,bits:f,blockSize:g})},855105:(a,b,c,d,f,g)=>{e.kb("MultiHeadAttention",a,{numHeads:b,isUnidirectional:c,maskFilterValue:d,scale:f,doRotary:g})},855264:(a,b)=>{e.kb("QuickGelu",a,{alpha:b})},855328:(a,b,c,d,f)=>{e.kb("RotaryEmbedding",a,{interleaved:!!b,numHeads:c,rotaryEmbeddingDim:d,
scale:f})},855467:(a,b,c)=>{e.kb("SkipLayerNormalization",a,{epsilon:b,simplified:!!c})},855569:(a,b,c)=>{e.kb("SkipLayerNormalization",a,{epsilon:b,simplified:!!c})},855671:(a,b,c,d)=>{e.kb("GatherBlockQuantized",a,{gatherAxis:b,quantizeAxis:c,blockSize:d})},855792:a=>{e.$b(a)},855826:(a,b)=>e.bc(Number(a),Number(b),e.Gb.ec,e.Gb.errors)};function db(a,b,c){return Fc(async()=>{await e.Yb(Number(a),Number(b),Number(c))})}function cb(){return"undefined"!==typeof wasmOffsetConverter}
class Gc{name="ExitStatus";constructor(a){this.message=`Program terminated with exit(${a})`;this.status=a}}
var Hc=a=>{a.terminate();a.onmessage=()=>{}},Ic=[],Mc=a=>{0==N.length&&(Jc(),Kc(N[0]));var b=N.pop();if(!b)return 6;Lc.push(b);O[a.Bb]=b;b.Bb=a.Bb;var c={Cb:"run",hc:a.fc,Ib:a.Ib,Bb:a.Bb};n&&b.unref();b.postMessage(c,a.Nb);return 0},P=0,Q=(a,b,...c)=>{for(var d=2*c.length,f=Nc(),g=Oc(8*d),h=g>>>3,l=0;l<c.length;l++){var m=c[l];"bigint"==typeof m?(C[h+2*l]=1n,C[h+2*l+1]=m):(C[h+2*l]=0n,J()[h+2*l+1>>>0]=m)}a=Pc(a,0,d,g,b);Qc(f);return a};
function Dc(a){if(q)return Q(0,1,a);wa=a;if(!(0<P)){for(var b of Lc)Hc(b);for(b of N)Hc(b);N=[];Lc=[];O={};A=!0}ma(a,new Gc(a))}function Rc(a){if(q)return Q(1,0,a);yc(a)}var yc=a=>{wa=a;if(q)throw Rc(a),"unwind";Dc(a)},N=[],Lc=[],Sc=[],O={};function Tc(){for(var a=e.numThreads-1;a--;)Jc();Ic.unshift(()=>{Ua++;Uc(()=>Wa())})}var Wc=a=>{var b=a.Bb;delete O[b];N.push(a);Lc.splice(Lc.indexOf(a),1);a.Bb=0;Vc(b)};function Na(){Sc.forEach(a=>a())}
var Kc=a=>new Promise(b=>{a.onmessage=g=>{g=g.data;var h=g.Cb;if(g.Hb&&g.Hb!=Ka()){var l=O[g.Hb];l?l.postMessage(g,g.Nb):x(`Internal error! Worker sent a message "${h}" to target pthread ${g.Hb}, but that thread no longer exists!`)}else if("checkMailbox"===h)Ra();else if("spawnThread"===h)Mc(g);else if("cleanupThread"===h)Wc(O[g.ic]);else if("loaded"===h)a.loaded=!0,n&&!a.Bb&&a.unref(),b(a);else if("alert"===h)alert(`Thread ${g.jc}: ${g.text}`);else if("setimmediate"===g.target)a.postMessage(g);else if("callHandler"===
h)e[g.Rb](...g.args);else h&&x(`worker sent an unknown command ${h}`)};a.onerror=g=>{x(`${"worker sent an error!"} ${g.filename}:${g.lineno}: ${g.message}`);throw g;};n&&(a.on("message",g=>a.onmessage({data:g})),a.on("error",g=>a.onerror(g)));var c=[],d=[],f;for(f of d)e.propertyIsEnumerable(f)&&c.push(f);a.postMessage({Cb:"load",Sb:c,lc:z,mc:va})});function Uc(a){q?a():Promise.all(N.map(Kc)).then(a)}
function Jc(){var a=new Worker(new URL(import.meta.url),{type:"module",workerData:"em-pthread",name:"em-pthread"});N.push(a)}var La=a=>{E();var b=I()[a+52>>>2>>>0];a=I()[a+56>>>2>>>0];Xc(b,b-a);Qc(b)},Qa=(a,b)=>{P=0;a=Yc(a,b);0<P?wa=a:Zc(a)};class $c{constructor(a){this.Jb=a-24}}var ad=0,bd=0;function eb(a,b,c){a>>>=0;var d=new $c(a);b>>>=0;c>>>=0;I()[d.Jb+16>>>2>>>0]=0;I()[d.Jb+4>>>2>>>0]=b;I()[d.Jb+8>>>2>>>0]=c;ad=a;bd++;throw ad;}
function cd(a,b,c,d){return q?Q(2,1,a,b,c,d):fb(a,b,c,d)}function fb(a,b,c,d){a>>>=0;b>>>=0;c>>>=0;d>>>=0;if("undefined"==typeof SharedArrayBuffer)return 6;var f=[];if(q&&0===f.length)return cd(a,b,c,d);a={fc:c,Bb:a,Ib:d,Nb:f};return q?(a.Cb="spawnThread",postMessage(a,f),0):Mc(a)}
var dd="undefined"!=typeof TextDecoder?new TextDecoder:void 0,ed=(a,b=0,c=NaN)=>{b>>>=0;var d=b+c;for(c=b;a[c]&&!(c>=d);)++c;if(16<c-b&&a.buffer&&dd)return dd.decode(a.buffer instanceof ArrayBuffer?a.subarray(b,c):a.slice(b,c));for(d="";b<c;){var f=a[b++];if(f&128){var g=a[b++]&63;if(192==(f&224))d+=String.fromCharCode((f&31)<<6|g);else{var h=a[b++]&63;f=224==(f&240)?(f&15)<<12|g<<6|h:(f&7)<<18|g<<12|h<<6|a[b++]&63;65536>f?d+=String.fromCharCode(f):(f-=65536,d+=String.fromCharCode(55296|f>>10,56320|
f&1023))}}else d+=String.fromCharCode(f)}return d},M=(a,b)=>(a>>>=0)?ed(F(),a,b):"";function gb(a,b,c){return q?Q(3,1,a,b,c):0}function hb(a,b){if(q)return Q(4,1,a,b)}
var fd=a=>{for(var b=0,c=0;c<a.length;++c){var d=a.charCodeAt(c);127>=d?b++:2047>=d?b+=2:55296<=d&&57343>=d?(b+=4,++c):b+=3}return b},gd=(a,b,c)=>{var d=F();b>>>=0;if(0<c){var f=b;c=b+c-1;for(var g=0;g<a.length;++g){var h=a.charCodeAt(g);if(55296<=h&&57343>=h){var l=a.charCodeAt(++g);h=65536+((h&1023)<<10)|l&1023}if(127>=h){if(b>=c)break;d[b++>>>0]=h}else{if(2047>=h){if(b+1>=c)break;d[b++>>>0]=192|h>>6}else{if(65535>=h){if(b+2>=c)break;d[b++>>>0]=224|h>>12}else{if(b+3>=c)break;d[b++>>>0]=240|h>>18;
d[b++>>>0]=128|h>>12&63}d[b++>>>0]=128|h>>6&63}d[b++>>>0]=128|h&63}}d[b>>>0]=0;a=b-f}else a=0;return a};function ib(a,b){if(q)return Q(5,1,a,b)}function jb(a,b,c){if(q)return Q(6,1,a,b,c)}function kb(a,b,c){return q?Q(7,1,a,b,c):0}function lb(a,b){if(q)return Q(8,1,a,b)}function mb(a,b,c){if(q)return Q(9,1,a,b,c)}function nb(a,b,c,d){if(q)return Q(10,1,a,b,c,d)}function ob(a,b,c,d){if(q)return Q(11,1,a,b,c,d)}function pb(a,b,c,d){if(q)return Q(12,1,a,b,c,d)}function qb(a){if(q)return Q(13,1,a)}
function rb(a,b){if(q)return Q(14,1,a,b)}function sb(a,b,c){if(q)return Q(15,1,a,b,c)}var tb=()=>L(""),hd,R=a=>{for(var b="";F()[a>>>0];)b+=hd[F()[a++>>>0]];return b},jd={},kd={},ld={},S;function md(a,b,c={}){var d=b.name;if(!a)throw new S(`type "${d}" must have a positive integer typeid pointer`);if(kd.hasOwnProperty(a)){if(c.Tb)return;throw new S(`Cannot register type '${d}' twice`);}kd[a]=b;delete ld[a];jd.hasOwnProperty(a)&&(b=jd[a],delete jd[a],b.forEach(f=>f()))}
function T(a,b,c={}){return md(a,b,c)}var nd=(a,b,c)=>{switch(b){case 1:return c?d=>D()[d>>>0]:d=>F()[d>>>0];case 2:return c?d=>G()[d>>>1>>>0]:d=>Fa()[d>>>1>>>0];case 4:return c?d=>H()[d>>>2>>>0]:d=>I()[d>>>2>>>0];case 8:return c?d=>C[d>>>3]:d=>Da[d>>>3];default:throw new TypeError(`invalid integer width (${b}): ${a}`);}};
function ub(a,b,c){a>>>=0;c>>>=0;b=R(b>>>0);T(a,{name:b,fromWireType:d=>d,toWireType:function(d,f){if("bigint"!=typeof f&&"number"!=typeof f)throw null===f?f="null":(d=typeof f,f="object"===d||"array"===d||"function"===d?f.toString():""+f),new TypeError(`Cannot convert "${f}" to ${this.name}`);"number"==typeof f&&(f=BigInt(f));return f},Db:U,readValueFromPointer:nd(b,c,-1==b.indexOf("u")),Eb:null})}var U=8;
function vb(a,b,c,d){a>>>=0;b=R(b>>>0);T(a,{name:b,fromWireType:function(f){return!!f},toWireType:function(f,g){return g?c:d},Db:U,readValueFromPointer:function(f){return this.fromWireType(F()[f>>>0])},Eb:null})}var od=[],V=[];function Ob(a){a>>>=0;9<a&&0===--V[a+1]&&(V[a]=void 0,od.push(a))}
var W=a=>{if(!a)throw new S("Cannot use deleted val. handle = "+a);return V[a]},X=a=>{switch(a){case void 0:return 2;case null:return 4;case !0:return 6;case !1:return 8;default:const b=od.pop()||V.length;V[b]=a;V[b+1]=1;return b}};function pd(a){return this.fromWireType(I()[a>>>2>>>0])}var qd={name:"emscripten::val",fromWireType:a=>{var b=W(a);Ob(a);return b},toWireType:(a,b)=>X(b),Db:U,readValueFromPointer:pd,Eb:null};function wb(a){return T(a>>>0,qd)}
var rd=(a,b)=>{switch(b){case 4:return function(c){return this.fromWireType(Ga()[c>>>2>>>0])};case 8:return function(c){return this.fromWireType(J()[c>>>3>>>0])};default:throw new TypeError(`invalid float width (${b}): ${a}`);}};function xb(a,b,c){a>>>=0;c>>>=0;b=R(b>>>0);T(a,{name:b,fromWireType:d=>d,toWireType:(d,f)=>f,Db:U,readValueFromPointer:rd(b,c),Eb:null})}
function yb(a,b,c,d,f){a>>>=0;c>>>=0;b=R(b>>>0);-1===f&&(f=4294967295);f=l=>l;if(0===d){var g=32-8*c;f=l=>l<<g>>>g}var h=b.includes("unsigned")?function(l,m){return m>>>0}:function(l,m){return m};T(a,{name:b,fromWireType:f,toWireType:h,Db:U,readValueFromPointer:nd(b,c,0!==d),Eb:null})}
function zb(a,b,c){function d(g){var h=I()[g>>>2>>>0];g=I()[g+4>>>2>>>0];return new f(D().buffer,g,h)}a>>>=0;var f=[Int8Array,Uint8Array,Int16Array,Uint16Array,Int32Array,Uint32Array,Float32Array,Float64Array,BigInt64Array,BigUint64Array][b];c=R(c>>>0);T(a,{name:c,fromWireType:d,Db:U,readValueFromPointer:d},{Tb:!0})}
function Ab(a,b){a>>>=0;b=R(b>>>0);T(a,{name:b,fromWireType:function(c){for(var d=I()[c>>>2>>>0],f=c+4,g,h=f,l=0;l<=d;++l){var m=f+l;if(l==d||0==F()[m>>>0])h=M(h,m-h),void 0===g?g=h:(g+=String.fromCharCode(0),g+=h),h=m+1}Y(c);return g},toWireType:function(c,d){d instanceof ArrayBuffer&&(d=new Uint8Array(d));var f="string"==typeof d;if(!(f||d instanceof Uint8Array||d instanceof Uint8ClampedArray||d instanceof Int8Array))throw new S("Cannot pass non-string to std::string");var g=f?fd(d):d.length;var h=
sd(4+g+1),l=h+4;I()[h>>>2>>>0]=g;if(f)gd(d,l,g+1);else if(f)for(f=0;f<g;++f){var m=d.charCodeAt(f);if(255<m)throw Y(h),new S("String has UTF-16 code units that do not fit in 8 bits");F()[l+f>>>0]=m}else for(f=0;f<g;++f)F()[l+f>>>0]=d[f];null!==c&&c.push(Y,h);return h},Db:U,readValueFromPointer:pd,Eb(c){Y(c)}})}
var td="undefined"!=typeof TextDecoder?new TextDecoder("utf-16le"):void 0,ud=(a,b)=>{var c=a>>1;for(var d=c+b/2;!(c>=d)&&Fa()[c>>>0];)++c;c<<=1;if(32<c-a&&td)return td.decode(F().slice(a,c));c="";for(d=0;!(d>=b/2);++d){var f=G()[a+2*d>>>1>>>0];if(0==f)break;c+=String.fromCharCode(f)}return c},vd=(a,b,c)=>{c??=2147483647;if(2>c)return 0;c-=2;var d=b;c=c<2*a.length?c/2:a.length;for(var f=0;f<c;++f){var g=a.charCodeAt(f);G()[b>>>1>>>0]=g;b+=2}G()[b>>>1>>>0]=0;return b-d},wd=a=>2*a.length,xd=(a,b)=>{for(var c=
0,d="";!(c>=b/4);){var f=H()[a+4*c>>>2>>>0];if(0==f)break;++c;65536<=f?(f-=65536,d+=String.fromCharCode(55296|f>>10,56320|f&1023)):d+=String.fromCharCode(f)}return d},yd=(a,b,c)=>{b>>>=0;c??=2147483647;if(4>c)return 0;var d=b;c=d+c-4;for(var f=0;f<a.length;++f){var g=a.charCodeAt(f);if(55296<=g&&57343>=g){var h=a.charCodeAt(++f);g=65536+((g&1023)<<10)|h&1023}H()[b>>>2>>>0]=g;b+=4;if(b+4>c)break}H()[b>>>2>>>0]=0;return b-d},zd=a=>{for(var b=0,c=0;c<a.length;++c){var d=a.charCodeAt(c);55296<=d&&57343>=
d&&++c;b+=4}return b};
function Bb(a,b,c){a>>>=0;b>>>=0;c>>>=0;c=R(c);if(2===b){var d=ud;var f=vd;var g=wd;var h=l=>Fa()[l>>>1>>>0]}else 4===b&&(d=xd,f=yd,g=zd,h=l=>I()[l>>>2>>>0]);T(a,{name:c,fromWireType:l=>{for(var m=I()[l>>>2>>>0],p,r=l+4,u=0;u<=m;++u){var w=l+4+u*b;if(u==m||0==h(w))r=d(r,w-r),void 0===p?p=r:(p+=String.fromCharCode(0),p+=r),r=w+b}Y(l);return p},toWireType:(l,m)=>{if("string"!=typeof m)throw new S(`Cannot pass non-string to C++ string type ${c}`);var p=g(m),r=sd(4+p+b);I()[r>>>2>>>0]=p/b;f(m,r+4,p+b);
null!==l&&l.push(Y,r);return r},Db:U,readValueFromPointer:pd,Eb(l){Y(l)}})}function Cb(a,b){a>>>=0;b=R(b>>>0);T(a,{Ub:!0,name:b,Db:0,fromWireType:()=>{},toWireType:()=>{}})}function Db(a){Ma(a>>>0,!k,1,!ea,131072,!1);Na()}var Ad=a=>{if(!A)try{if(a(),!(0<P))try{q?Zc(wa):yc(wa)}catch(b){b instanceof Gc||"unwind"==b||ma(1,b)}}catch(b){b instanceof Gc||"unwind"==b||ma(1,b)}};
function Oa(a){a>>>=0;"function"===typeof Atomics.kc&&(Atomics.kc(H(),a>>>2,a).value.then(Ra),a+=128,Atomics.store(H(),a>>>2,1))}var Ra=()=>{var a=Ka();a&&(Oa(a),Ad(Bd))};function Eb(a,b){a>>>=0;a==b>>>0?setTimeout(Ra):q?postMessage({Hb:a,Cb:"checkMailbox"}):(a=O[a])&&a.postMessage({Cb:"checkMailbox"})}var Cd=[];function Fb(a,b,c,d,f){b>>>=0;d/=2;Cd.length=d;c=f>>>0>>>3;for(f=0;f<d;f++)Cd[f]=C[c+2*f]?C[c+2*f+1]:J()[c+2*f+1>>>0];return(b?Ec[b]:Dd[a])(...Cd)}var Gb=()=>{P=0};
function Hb(a){a>>>=0;q?postMessage({Cb:"cleanupThread",ic:a}):Wc(O[a])}function Ib(a){n&&O[a>>>0].ref()}var Fd=(a,b)=>{var c=kd[a];if(void 0===c)throw a=Ed(a),c=R(a),Y(a),new S(`${b} has unknown type ${c}`);return c},Gd=(a,b,c)=>{var d=[];a=a.toWireType(d,c);d.length&&(I()[b>>>2>>>0]=X(d));return a};function Jb(a,b,c){b>>>=0;c>>>=0;a=W(a>>>0);b=Fd(b,"emval::as");return Gd(b,c,a)}function Kb(a,b){b>>>=0;a=W(a>>>0);b=Fd(b,"emval::as");return b.toWireType(null,a)}var Hd=a=>{try{a()}catch(b){L(b)}};
function Id(){var a=K,b={};for(let [c,d]of Object.entries(a))b[c]="function"==typeof d?(...f)=>{Jd.push(c);try{return d(...f)}finally{A||(Jd.pop(),t&&1===Z&&0===Jd.length&&(Z=0,P+=1,Hd(Kd),"undefined"!=typeof Fibers&&Fibers.sc()))}}:d;return b}var Z=0,t=null,Ld=0,Jd=[],Md={},Nd={},Od=0,Pd=null,Qd=[];function ia(){return new Promise((a,b)=>{Pd={resolve:a,reject:b}})}
function Rd(){var a=sd(65548),b=a+12;I()[a>>>2>>>0]=b;I()[a+4>>>2>>>0]=b+65536;b=Jd[0];var c=Md[b];void 0===c&&(c=Od++,Md[b]=c,Nd[c]=b);b=c;H()[a+8>>>2>>>0]=b;return a}function Sd(){var a=H()[t+8>>>2>>>0];a=K[Nd[a]];--P;return a()}
function Td(a){if(!A){if(0===Z){var b=!1,c=!1;a((d=0)=>{if(!A&&(Ld=d,b=!0,c)){Z=2;Hd(()=>Ud(t));"undefined"!=typeof MainLoop&&MainLoop.Qb&&MainLoop.resume();d=!1;try{var f=Sd()}catch(l){f=l,d=!0}var g=!1;if(!t){var h=Pd;h&&(Pd=null,(d?h.reject:h.resolve)(f),g=!0)}if(d&&!g)throw f;}});c=!0;b||(Z=1,t=Rd(),"undefined"!=typeof MainLoop&&MainLoop.Qb&&MainLoop.pause(),Hd(()=>Vd(t)))}else 2===Z?(Z=0,Hd(Xd),Y(t),t=null,Qd.forEach(Ad)):L(`invalid state: ${Z}`);return Ld}}
function Fc(a){return Td(b=>{a().then(b)})}function Lb(a){a>>>=0;return Fc(async()=>{var b=await W(a);return X(b)})}var Yd=[];function Mb(a,b,c,d){c>>>=0;d>>>=0;a=Yd[a>>>0];b=W(b>>>0);return a(null,b,c,d)}var Zd={},$d=a=>{var b=Zd[a];return void 0===b?R(a):b};function Nb(a,b,c,d,f){c>>>=0;d>>>=0;f>>>=0;a=Yd[a>>>0];b=W(b>>>0);c=$d(c);return a(b,b[c],d,f)}function Pb(a,b){b>>>=0;a=W(a>>>0);b=W(b);return a==b}var ae=()=>"object"==typeof globalThis?globalThis:Function("return this")();
function Qb(a){a>>>=0;if(0===a)return X(ae());a=$d(a);return X(ae()[a])}var be=a=>{var b=Yd.length;Yd.push(a);return b},ce=(a,b)=>{for(var c=Array(a),d=0;d<a;++d)c[d]=Fd(I()[b+4*d>>>2>>>0],"parameter "+d);return c},de=(a,b)=>Object.defineProperty(b,"name",{value:a});
function ee(a){var b=Function;if(!(b instanceof Function))throw new TypeError(`new_ called with constructor type ${typeof b} which is not a function`);var c=de(b.name||"unknownFunctionName",function(){});c.prototype=b.prototype;c=new c;a=b.apply(c,a);return a instanceof Object?a:c}
function Rb(a,b,c){b=ce(a,b>>>0);var d=b.shift();a--;var f="return function (obj, func, destructorsRef, args) {\n",g=0,h=[];0===c&&h.push("obj");for(var l=["retType"],m=[d],p=0;p<a;++p)h.push("arg"+p),l.push("argType"+p),m.push(b[p]),f+=`  var arg${p} = argType${p}.readValueFromPointer(args${g?"+"+g:""});\n`,g+=b[p].Db;f+=`  var rv = ${1===c?"new func":"func.call"}(${h.join(", ")});\n`;d.Ub||(l.push("emval_returnValue"),m.push(Gd),f+="  return emval_returnValue(retType, destructorsRef, rv);\n");l.push(f+
"};\n");a=ee(l)(...m);c=`methodCaller<(${b.map(r=>r.name).join(", ")}) => ${d.name}>`;return be(de(c,a))}function Sb(a){a=$d(a>>>0);return X(e[a])}function Tb(a,b){b>>>=0;a=W(a>>>0);b=W(b);return X(a[b])}function Ub(a){a>>>=0;9<a&&(V[a+1]+=1)}function Vb(){return X([])}function Wb(a){a=W(a>>>0);for(var b=Array(a.length),c=0;c<a.length;c++)b[c]=a[c];return X(b)}function Xb(a){return X($d(a>>>0))}function Yb(){return X({})}
function Zb(a){a>>>=0;for(var b=W(a);b.length;){var c=b.pop();b.pop()(c)}Ob(a)}function $b(a,b,c){b>>>=0;c>>>=0;a=W(a>>>0);b=W(b);c=W(c);a[b]=c}function ac(a,b){b>>>=0;a=Fd(a>>>0,"_emval_take_value");a=a.readValueFromPointer(b);return X(a)}
function bc(a,b){a=-9007199254740992>a||9007199254740992<a?NaN:Number(a);b>>>=0;a=new Date(1E3*a);H()[b>>>2>>>0]=a.getUTCSeconds();H()[b+4>>>2>>>0]=a.getUTCMinutes();H()[b+8>>>2>>>0]=a.getUTCHours();H()[b+12>>>2>>>0]=a.getUTCDate();H()[b+16>>>2>>>0]=a.getUTCMonth();H()[b+20>>>2>>>0]=a.getUTCFullYear()-1900;H()[b+24>>>2>>>0]=a.getUTCDay();a=(a.getTime()-Date.UTC(a.getUTCFullYear(),0,1,0,0,0,0))/864E5|0;H()[b+28>>>2>>>0]=a}
var fe=a=>0===a%4&&(0!==a%100||0===a%400),ge=[0,31,60,91,121,152,182,213,244,274,305,335],he=[0,31,59,90,120,151,181,212,243,273,304,334];
function cc(a,b){a=-9007199254740992>a||9007199254740992<a?NaN:Number(a);b>>>=0;a=new Date(1E3*a);H()[b>>>2>>>0]=a.getSeconds();H()[b+4>>>2>>>0]=a.getMinutes();H()[b+8>>>2>>>0]=a.getHours();H()[b+12>>>2>>>0]=a.getDate();H()[b+16>>>2>>>0]=a.getMonth();H()[b+20>>>2>>>0]=a.getFullYear()-1900;H()[b+24>>>2>>>0]=a.getDay();var c=(fe(a.getFullYear())?ge:he)[a.getMonth()]+a.getDate()-1|0;H()[b+28>>>2>>>0]=c;H()[b+36>>>2>>>0]=-(60*a.getTimezoneOffset());c=(new Date(a.getFullYear(),6,1)).getTimezoneOffset();
var d=(new Date(a.getFullYear(),0,1)).getTimezoneOffset();a=(c!=d&&a.getTimezoneOffset()==Math.min(d,c))|0;H()[b+32>>>2>>>0]=a}
function dc(a){a>>>=0;var b=new Date(H()[a+20>>>2>>>0]+1900,H()[a+16>>>2>>>0],H()[a+12>>>2>>>0],H()[a+8>>>2>>>0],H()[a+4>>>2>>>0],H()[a>>>2>>>0],0),c=H()[a+32>>>2>>>0],d=b.getTimezoneOffset(),f=(new Date(b.getFullYear(),6,1)).getTimezoneOffset(),g=(new Date(b.getFullYear(),0,1)).getTimezoneOffset(),h=Math.min(g,f);0>c?H()[a+32>>>2>>>0]=Number(f!=g&&h==d):0<c!=(h==d)&&(f=Math.max(g,f),b.setTime(b.getTime()+6E4*((0<c?h:f)-d)));H()[a+24>>>2>>>0]=b.getDay();c=(fe(b.getFullYear())?ge:he)[b.getMonth()]+
b.getDate()-1|0;H()[a+28>>>2>>>0]=c;H()[a>>>2>>>0]=b.getSeconds();H()[a+4>>>2>>>0]=b.getMinutes();H()[a+8>>>2>>>0]=b.getHours();H()[a+12>>>2>>>0]=b.getDate();H()[a+16>>>2>>>0]=b.getMonth();H()[a+20>>>2>>>0]=b.getYear();a=b.getTime();return BigInt(isNaN(a)?-1:a/1E3)}function ec(a,b,c,d,f,g,h){return q?Q(16,1,a,b,c,d,f,g,h):-52}function fc(a,b,c,d,f,g){if(q)return Q(17,1,a,b,c,d,f,g)}var ie={},qc=()=>performance.timeOrigin+performance.now();
function gc(a,b){if(q)return Q(18,1,a,b);ie[a]&&(clearTimeout(ie[a].id),delete ie[a]);if(!b)return 0;var c=setTimeout(()=>{delete ie[a];Ad(()=>je(a,performance.timeOrigin+performance.now()))},b);ie[a]={id:c,rc:b};return 0}
function hc(a,b,c,d){a>>>=0;b>>>=0;c>>>=0;d>>>=0;var f=(new Date).getFullYear(),g=(new Date(f,0,1)).getTimezoneOffset();f=(new Date(f,6,1)).getTimezoneOffset();var h=Math.max(g,f);I()[a>>>2>>>0]=60*h;H()[b>>>2>>>0]=Number(g!=f);b=l=>{var m=Math.abs(l);return`UTC${0<=l?"-":"+"}${String(Math.floor(m/60)).padStart(2,"0")}${String(m%60).padStart(2,"0")}`};a=b(g);b=b(f);f<g?(gd(a,c,17),gd(b,d,17)):(gd(a,d,17),gd(b,c,17))}var mc=()=>Date.now(),ke=1;
function ic(a,b,c){if(!(0<=a&&3>=a))return 28;if(0===a)a=Date.now();else if(ke)a=performance.timeOrigin+performance.now();else return 52;C[c>>>0>>>3]=BigInt(Math.round(1E6*a));return 0}var le=[],me=(a,b)=>{le.length=0;for(var c;c=F()[a++>>>0];){var d=105!=c;d&=112!=c;b+=d&&b%8?4:0;le.push(112==c?I()[b>>>2>>>0]:106==c?C[b>>>3]:105==c?H()[b>>>2>>>0]:J()[b>>>3>>>0]);b+=d?8:4}return le};function jc(a,b,c){a>>>=0;b=me(b>>>0,c>>>0);return Ec[a](...b)}
function kc(a,b,c){a>>>=0;b=me(b>>>0,c>>>0);return Ec[a](...b)}var lc=()=>{};function nc(a,b){return x(M(a>>>0,b>>>0))}var oc=()=>{P+=1;throw"unwind";};function pc(){return 4294901760}var rc=()=>n?require("os").cpus().length:navigator.hardwareConcurrency;function sc(){L("Cannot use emscripten_pc_get_function without -sUSE_OFFSET_CONVERTER");return 0}
function tc(a){a>>>=0;var b=F().length;if(a<=b||4294901760<a)return!1;for(var c=1;4>=c;c*=2){var d=b*(1+.2/c);d=Math.min(d,a+100663296);a:{d=(Math.min(4294901760,65536*Math.ceil(Math.max(a,d)/65536))-z.buffer.byteLength+65535)/65536|0;try{z.grow(d);E();var f=1;break a}catch(g){}f=void 0}if(f)return!0}return!1}var ne=()=>{L("Cannot use convertFrameToPC (needed by __builtin_return_address) without -sUSE_OFFSET_CONVERTER");return 0},oe={},pe=a=>{a.forEach(b=>{var c=ne();c&&(oe[c]=b)})};
function uc(){var a=Error().stack.toString().split("\n");"Error"==a[0]&&a.shift();pe(a);oe.Mb=ne();oe.dc=a;return oe.Mb}function vc(a,b,c){a>>>=0;b>>>=0;if(oe.Mb==a)var d=oe.dc;else d=Error().stack.toString().split("\n"),"Error"==d[0]&&d.shift(),pe(d);for(var f=3;d[f]&&ne()!=a;)++f;for(a=0;a<c&&d[a+f];++a)H()[b+4*a>>>2>>>0]=ne();return a}
var qe={},se=()=>{if(!re){var a={USER:"web_user",LOGNAME:"web_user",PATH:"/",PWD:"/",HOME:"/home/<USER>",LANG:("object"==typeof navigator&&navigator.languages&&navigator.languages[0]||"C").replace("-","_")+".UTF-8",_:la||"./this.program"},b;for(b in qe)void 0===qe[b]?delete a[b]:a[b]=qe[b];var c=[];for(b in a)c.push(`${b}=${a[b]}`);re=c}return re},re;
function wc(a,b){if(q)return Q(19,1,a,b);a>>>=0;b>>>=0;var c=0;se().forEach((d,f)=>{var g=b+c;f=I()[a+4*f>>>2>>>0]=g;for(g=0;g<d.length;++g)D()[f++>>>0]=d.charCodeAt(g);D()[f>>>0]=0;c+=d.length+1});return 0}function xc(a,b){if(q)return Q(20,1,a,b);a>>>=0;b>>>=0;var c=se();I()[a>>>2>>>0]=c.length;var d=0;c.forEach(f=>d+=f.length+1);I()[b>>>2>>>0]=d;return 0}function zc(a){return q?Q(21,1,a):52}function Ac(a,b,c,d){return q?Q(22,1,a,b,c,d):52}function Bc(a,b,c,d){return q?Q(23,1,a,b,c,d):70}
var te=[null,[],[]];function Cc(a,b,c,d){if(q)return Q(24,1,a,b,c,d);b>>>=0;c>>>=0;d>>>=0;for(var f=0,g=0;g<c;g++){var h=I()[b>>>2>>>0],l=I()[b+4>>>2>>>0];b+=8;for(var m=0;m<l;m++){var p=F()[h+m>>>0],r=te[a];0===p||10===p?((1===a?ta:x)(ed(r)),r.length=0):r.push(p)}f+=l}I()[d>>>2>>>0]=f;return 0}q||Tc();for(var ue=Array(256),ve=0;256>ve;++ve)ue[ve]=String.fromCharCode(ve);hd=ue;S=e.BindingError=class extends Error{constructor(a){super(a);this.name="BindingError"}};
e.InternalError=class extends Error{constructor(a){super(a);this.name="InternalError"}};V.push(0,1,void 0,1,null,1,!0,1,!1,1);e.count_emval_handles=()=>V.length/2-5-od.length;var Dd=[Dc,Rc,cd,gb,hb,ib,jb,kb,lb,mb,nb,ob,pb,qb,rb,sb,ec,fc,gc,wc,xc,zc,Ac,Bc,Cc],bb,K;
(async function(){function a(d,f){K=d.exports;K=Id();K=we();Sc.push(K.jb);va=f;Wa();return K}Ua++;var b=ab();if(e.instantiateWasm)return new Promise(d=>{e.instantiateWasm(b,(f,g)=>{a(f,g);d(f.exports)})});if(q)return new Promise(d=>{Ha=f=>{var g=new WebAssembly.Instance(f,ab());d(a(g,f))}});Xa??=e.locateFile?e.locateFile?e.locateFile("ort-wasm-simd-threaded.jsep.wasm",v):v+"ort-wasm-simd-threaded.jsep.wasm":(new URL("ort-wasm-simd-threaded.jsep.wasm",import.meta.url)).href;try{var c=await $a(b);
return a(c.instance,c.module)}catch(d){return ca(d),Promise.reject(d)}})();var Ed=a=>(Ed=K.Ea)(a),Pa=()=>(Pa=K.Fa)();e._OrtInit=(a,b)=>(e._OrtInit=K.Ga)(a,b);e._OrtGetLastError=(a,b)=>(e._OrtGetLastError=K.Ha)(a,b);e._OrtCreateSessionOptions=(a,b,c,d,f,g,h,l,m,p)=>(e._OrtCreateSessionOptions=K.Ia)(a,b,c,d,f,g,h,l,m,p);e._OrtAppendExecutionProvider=(a,b,c,d,f)=>(e._OrtAppendExecutionProvider=K.Ja)(a,b,c,d,f);e._OrtAddFreeDimensionOverride=(a,b,c)=>(e._OrtAddFreeDimensionOverride=K.Ka)(a,b,c);
e._OrtAddSessionConfigEntry=(a,b,c)=>(e._OrtAddSessionConfigEntry=K.La)(a,b,c);e._OrtReleaseSessionOptions=a=>(e._OrtReleaseSessionOptions=K.Ma)(a);e._OrtCreateSession=(a,b,c)=>(e._OrtCreateSession=K.Na)(a,b,c);e._OrtReleaseSession=a=>(e._OrtReleaseSession=K.Oa)(a);e._OrtGetInputOutputCount=(a,b,c)=>(e._OrtGetInputOutputCount=K.Pa)(a,b,c);e._OrtGetInputOutputMetadata=(a,b,c,d)=>(e._OrtGetInputOutputMetadata=K.Qa)(a,b,c,d);e._OrtFree=a=>(e._OrtFree=K.Ra)(a);
e._OrtCreateTensor=(a,b,c,d,f,g)=>(e._OrtCreateTensor=K.Sa)(a,b,c,d,f,g);e._OrtGetTensorData=(a,b,c,d,f)=>(e._OrtGetTensorData=K.Ta)(a,b,c,d,f);e._OrtReleaseTensor=a=>(e._OrtReleaseTensor=K.Ua)(a);e._OrtCreateRunOptions=(a,b,c,d)=>(e._OrtCreateRunOptions=K.Va)(a,b,c,d);e._OrtAddRunConfigEntry=(a,b,c)=>(e._OrtAddRunConfigEntry=K.Wa)(a,b,c);e._OrtReleaseRunOptions=a=>(e._OrtReleaseRunOptions=K.Xa)(a);e._OrtCreateBinding=a=>(e._OrtCreateBinding=K.Ya)(a);
e._OrtBindInput=(a,b,c)=>(e._OrtBindInput=K.Za)(a,b,c);e._OrtBindOutput=(a,b,c,d)=>(e._OrtBindOutput=K._a)(a,b,c,d);e._OrtClearBoundOutputs=a=>(e._OrtClearBoundOutputs=K.$a)(a);e._OrtReleaseBinding=a=>(e._OrtReleaseBinding=K.ab)(a);e._OrtRunWithBinding=(a,b,c,d,f)=>(e._OrtRunWithBinding=K.bb)(a,b,c,d,f);e._OrtRun=(a,b,c,d,f,g,h,l)=>(e._OrtRun=K.cb)(a,b,c,d,f,g,h,l);e._OrtEndProfiling=a=>(e._OrtEndProfiling=K.db)(a);e._JsepOutput=(a,b,c)=>(e._JsepOutput=K.eb)(a,b,c);
e._JsepGetNodeName=a=>(e._JsepGetNodeName=K.fb)(a);
var Ka=()=>(Ka=K.gb)(),Y=e._free=a=>(Y=e._free=K.hb)(a),sd=e._malloc=a=>(sd=e._malloc=K.ib)(a),Ma=(a,b,c,d,f,g)=>(Ma=K.lb)(a,b,c,d,f,g),Sa=()=>(Sa=K.mb)(),Pc=(a,b,c,d,f)=>(Pc=K.nb)(a,b,c,d,f),Vc=a=>(Vc=K.ob)(a),Zc=a=>(Zc=K.pb)(a),je=(a,b)=>(je=K.qb)(a,b),Bd=()=>(Bd=K.rb)(),Xc=(a,b)=>(Xc=K.sb)(a,b),Qc=a=>(Qc=K.tb)(a),Oc=a=>(Oc=K.ub)(a),Nc=()=>(Nc=K.vb)(),Yc=e.dynCall_ii=(a,b)=>(Yc=e.dynCall_ii=K.wb)(a,b),Vd=a=>(Vd=K.xb)(a),Kd=()=>(Kd=K.yb)(),Ud=a=>(Ud=K.zb)(a),Xd=()=>(Xd=K.Ab)();
function we(){var a=K;a=Object.assign({},a);var b=d=>f=>d(f)>>>0,c=d=>()=>d()>>>0;a.Ea=b(a.Ea);a.gb=c(a.gb);a.ib=b(a.ib);a.ub=b(a.ub);a.vb=c(a.vb);a.__cxa_get_exception_ptr=b(a.__cxa_get_exception_ptr);return a}e.stackSave=()=>Nc();e.stackRestore=a=>Qc(a);e.stackAlloc=a=>Oc(a);
e.setValue=function(a,b,c="i8"){c.endsWith("*")&&(c="*");switch(c){case "i1":D()[a>>>0]=b;break;case "i8":D()[a>>>0]=b;break;case "i16":G()[a>>>1>>>0]=b;break;case "i32":H()[a>>>2>>>0]=b;break;case "i64":C[a>>>3]=BigInt(b);break;case "float":Ga()[a>>>2>>>0]=b;break;case "double":J()[a>>>3>>>0]=b;break;case "*":I()[a>>>2>>>0]=b;break;default:L(`invalid type for setValue: ${c}`)}};
e.getValue=function(a,b="i8"){b.endsWith("*")&&(b="*");switch(b){case "i1":return D()[a>>>0];case "i8":return D()[a>>>0];case "i16":return G()[a>>>1>>>0];case "i32":return H()[a>>>2>>>0];case "i64":return C[a>>>3];case "float":return Ga()[a>>>2>>>0];case "double":return J()[a>>>3>>>0];case "*":return I()[a>>>2>>>0];default:L(`invalid type for getValue: ${b}`)}};e.UTF8ToString=M;e.stringToUTF8=gd;e.lengthBytesUTF8=fd;
function xe(){if(0<Ua)Va=xe;else if(q)aa(e),Ta();else{for(;0<Ic.length;)Ic.shift()(e);0<Ua?Va=xe:(e.calledRun=!0,A||(Ta(),aa(e)))}}xe();e.PTR_SIZE=4;moduleRtn=da;


  return moduleRtn;
}
);
})();
export default ortWasmThreaded;
var isPthread = globalThis.self?.name?.startsWith('em-pthread');
var isNode = typeof globalThis.process?.versions?.node == 'string';
if (isNode) isPthread = (await import('worker_threads')).workerData === 'em-pthread';

// When running as a pthread, construct a new instance on startup
isPthread && ortWasmThreaded();
